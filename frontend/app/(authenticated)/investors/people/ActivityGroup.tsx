import Checkbox from '@/components/ui/Checkbox';
import { DiscreteSentiment, discreteSentiment } from '@/util/sentiment';
import { UserIcon } from '@heroicons/react/24/outline';
import { formatInTimeZone } from 'date-fns-tz';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { useActivitiesLayout } from '../activities/ActivityLayoutContext';
import { ActivityByPeople, usePeopleLayout } from './PeopleLayoutContext';
import HotcopperStatus from '@/components/HotcopperStatus';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';

export default function PeopleGroup({
    group,
    author,
    latestDate,
    averageSentiment,
    lastActivity,
    activitiesCount
}: ActivityByPeople) {
    const { selectedPeople, setSelectedPeople } = usePeopleLayout();

    const router = useRouter();
    const discrete = discreteSentiment(averageSentiment);
    const { setIsRightPanelExpanded } = useActivitiesLayout();

    const sentimentColor = useMemo(() => {
        if (discrete === DiscreteSentiment.NEGATIVE) return 'text-red-700';
        if (discrete === DiscreteSentiment.LACKING) return 'text-yellow-700';
        if (discrete === DiscreteSentiment.POSITIVE) return 'text-green-700';
        if (discrete === DiscreteSentiment.NEUTRAL) return 'text-slate-700';

        return '';
    }, [discrete]);

    const handleCheckboxClick = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation();
        setSelectedPeople((prev: string[]) => {
            if (e.target.checked) {
                return author.key && !prev.includes(author.key)
                    ? [...prev, author.key]
                    : prev;
            } else {
                return prev.filter((it) => it !== author.key);
            }
        });
    };

    return (
        <>
            <tr
                key={author?.key}
                className="cursor-pointer group text-sm font-normal text-qb-black-100 bg-qb-gray-30 hover:bg-qb-gray-125"
                onClick={() => {
                    setIsRightPanelExpanded(true);
                    router.push(`/investors/people/${encodeURIComponent(author.key)}`);
                }}>
                <td className="w-10 text-center bg-white border-y border-gray-200">
                    <Checkbox
                        onClick={(e) => e.stopPropagation()}
                        onChange={handleCheckboxClick}
                        checked={author.key ? selectedPeople.includes(author.key) : false}
                    />
                </td>
                <td className="whitespace-nowrap px-2 pr-6 bg-white hover:bg-qb-gray-125 border-y border-gray-200 py-3">
                    <div className="flex items-center gap-x-2">
                        {author?.image ? (
                            <img
                                src={author.image}
                                className="rounded-md size-5 object-cover"
                                alt=""
                            />
                        ) : (
                            <ActivitySourceIcon
                                className="rounded-md size-5 object-cover"
                                activity={lastActivity}
                            />
                        )}

                        <span className="flex-1 hover:underline hover:cursor-pointer">
                            {author?.name}
                        </span>
                    </div>
                </td>
                <td className="px-4 overflow-hidden break-all font-base text-right border border-gray-200 text-qb-gray-150">
                    <span className={sentimentColor}>{averageSentiment.toFixed(2)}</span>
                </td>
                <td className="px-4 overflow-hidden break-all font-base text-right border border-gray-200 text-qb-gray-150">
                    {activitiesCount}
                </td>
                <td className="px-4 overflow-hidden break-all font-base text-right border border-gray-200 text-qb-gray-150">
                    {formatInTimeZone(latestDate, 'Australia/Sydney', 'd MMMM yyyy')}
                </td>
                <td className="px-4 overflow-hidden break-all font-base text-right border border-gray-200 text-qb-gray-150">
                    {lastActivity?.type === 'hotcopper' ? (
                        <HotcopperStatus activity={lastActivity} />
                    ) : (
                        ''
                    )}
                </td>
            </tr>
        </>
    );
}
