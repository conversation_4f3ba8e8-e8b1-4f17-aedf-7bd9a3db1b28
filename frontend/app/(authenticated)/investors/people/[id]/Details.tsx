'use client';

import Button from '@/components/ui/Button';
import { FlagIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Author } from '@quarterback/types';
import EditPeople from './EditPeople';
import Stats from './Stats';
import useAuthorUpdateMutation from '@/api/hooks/mutations/useAuthorUpdateMutation';

export default function DetailTab({
    params,
    isDetailExpanded,
    setIsDetailExpanded,
    author
}: {
    params: { id: string };
    isDetailExpanded: boolean;
    setIsDetailExpanded: (value: boolean) => void;
    author?: Author;
}) {
    const flagPerson = () => {};

    return (
        <>
            <div className="flex-1 overflow-y-auto overflow-x-hidden px-4 bg-gray-50">
                <div className="flex flex-col justify-between h-full">
                    <div className="flex flex-col flex-1">
                        <Stats
                            params={params}
                            isDetailExpanded={isDetailExpanded}
                            setIsDetailExpanded={setIsDetailExpanded}
                        />

                        {/* activity detail card */}
                        {author && (
                            <div className="mt-1 flex flex-col  py-4">
                                <EditPeople author={author} />
                            </div>
                        )}
                    </div>
                </div>
            </div>
            {/* Sticky Footer */}
            <div className="bg-white border-t border-gray-200 sticky bottom-0 px-4 py-2 text-sm text-qb-gray-150">
                <div className="flex items-center justify-between">
                    <button onClick={flagPerson}>
                        <FlagIcon className="size-4" />
                    </button>
                    <div className="flex space-x-2">
                        <Button
                            size="sm"
                            variant="secondary"
                            type="reset"
                            form="edit-people"
                            icon={
                                <XMarkIcon
                                    aria-hidden="true"
                                    className="-ml-0.5 h-5 w-5"
                                />
                            }>
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            size="sm"
                            type="submit"
                            form="edit-people">
                            Save
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
}
