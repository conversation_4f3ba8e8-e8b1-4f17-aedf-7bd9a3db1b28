import useTimeSeries from '@/api/hooks/useTimeSeries';
import ContentCard from '@/components/cards/ContentCard';
import ActivityChart from '@/components/charts/activity';
import Initials from '@/components/Initials';
import Dropdown, { DropdownOption } from '@/components/ui/Dropdown';
import InitialColors from '@/util/InitialColors';
import { ListedEntity, Organisation } from '@quarterback/types';
import { ParentSize } from '@visx/responsive';
import React, { useEffect, useMemo, useState } from 'react';
import { DateRange, DayPicker } from 'react-day-picker';
import { startOfDay, sub, format } from 'date-fns';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import {
    CalendarDateRangeIcon,
    EllipsisVerticalIcon,
    TrashIcon
} from '@heroicons/react/24/outline';
import classNames from 'classnames';
import useActivities from '@/api/hooks/useActivities';
import useFollowers from '@/api/hooks/useFollowers';

type Props = {
    organisation: Organisation | undefined;
    entity: ListedEntity | undefined;
    onClick?: (datetime: string) => void;
    range: DateRange;
};
const formatLabels = [
    { label: 'Announcement', color: '#197aff' },
    { label: 'Chatter', color: '#f6e84c' },
    { label: 'Media', color: '#f5b7c7' },
    { label: 'Broadcast', color: '#6a7a20' },
    { label: 'Manual Activity', color: '#4b5563' }
];
const sourceLabels = [
    { label: 'ASX', color: '#002D72' },
    { label: 'Hotcopper', color: '#F4B400' },
    { label: 'Reddit', color: '#FF5700' },
    { label: 'LinkedIn', color: '#1273DE' },
    { label: 'Twitter', color: '#1DA1F2' }
];
const sentimentLabels = [
    { label: 'NEGATIVE', color: '#f87171' },
    { label: 'LACKING', color: '#facc15' },
    { label: 'NEUTRAL', color: '#e2e8f0' },
    { label: 'POSITIVE', color: '#4ade80' }
];
const TIME_RANGE_OPTIONS = [
    { value: '1D', name: '1D' },
    { value: '1W', name: '1W' },
    { value: '1M', name: '1M' },
    { value: '3M', name: '3M' }
];

const GROUP_BY_OPTIONS: DropdownOption[] = [
    { value: 'source', name: 'Source' },
    { value: 'format', name: 'Format' },
    { value: 'sentiment', name: 'Sentiment' }
];

const OVERLAY_OPTIONS: DropdownOption[] = [
    { value: 'volume', name: 'Volume' },
    { value: 'sentiment', name: 'Sentiment' },
    { value: 'followers', name: 'Followers' }
];

type TimeRangeOption = (typeof TIME_RANGE_OPTIONS)[number]['value'];
type OverlayOption = (typeof OVERLAY_OPTIONS)[number]['value'];

const TIME_RANGE_MAP: Record<string, { from: Date; to: Date }> = {
    '1D': { from: startOfDay(sub(new Date(), { days: 1 })), to: startOfDay(new Date()) },
    '1W': { from: startOfDay(sub(new Date(), { weeks: 1 })), to: startOfDay(new Date()) },
    '1M': {
        from: startOfDay(sub(new Date(), { months: 1 })),
        to: startOfDay(new Date())
    },
    '3M': { from: startOfDay(sub(new Date(), { months: 3 })), to: startOfDay(new Date()) }
};

export default function SharePriceVsActivity({
    organisation,
    entity,
    onClick,
    range: initialRange
}: Props) {
    const [groupBy, setGroupBy] = useState(GROUP_BY_OPTIONS[1].value);
    const [overlay, setOverlay] = useState<OverlayOption | undefined>(
        OVERLAY_OPTIONS[0].value
    );
    const [timeRange, setTimeRange] = useState<TimeRangeOption | undefined>(
        TIME_RANGE_OPTIONS[2].value
    );

    const [customRange, setCustomRange] = useState<DateRange>({
        from: initialRange.from,
        to: initialRange.to
    });

    useEffect(() => {
        if (timeRange) {
            const newRange = TIME_RANGE_MAP[timeRange];
            if (newRange) {
                setCustomRange(newRange);
            }
        }
    }, [timeRange]);

    const { data: activities = [] } = useActivities(
        organisation,
        entity,
        customRange.from!,
        customRange.to!
    );

    const { data: followers = { twitter: [], linkedIn: [] } } = useFollowers(
        entity,
        customRange.from!,
        customRange.to!
    );

    const { data: timeSeries } = useTimeSeries(
        entity
            ? {
                symbol: entity?.symbol,
                exchange: entity?.exchange
            }
            : undefined,
        customRange.from!,
        customRange.to!
    );

    function handleSelect(newRange: DateRange | undefined) {
        if (newRange?.from && newRange?.to) {
            setCustomRange({ from: newRange.from, to: newRange.to });
            setTimeRange(undefined);
        }
    }

    const labels =
        groupBy === 'source'
            ? sourceLabels
            : groupBy === 'format'
                ? formatLabels
                : sentimentLabels;

    return (
        <>
            <ContentCard
                title={
                    <div className="flex items-center justify-between w-full">
                        <div className="flex items-center text-base font-medium gap-2">
                            <span>Share price vs</span>
                            <Initials color={InitialColors.ACTIVITY} name="Activities" />
                            <span>Activities</span>
                        </div>
                        <div className="flex gap-2">
                            <Dropdown
                                options={GROUP_BY_OPTIONS}
                                selected={groupBy}
                                onChange={setGroupBy}
                                title="Group Activities By"
                            />
                            <div className="inline-flex rounded-md bg-qb-gray-50 px-1 items-center text-sm font-medium text-gray-600 shadow-inner">
                                {TIME_RANGE_OPTIONS.map((option) => (
                                    <span
                                        className=" border-r border-qb-gray-115"
                                        key={option.value}>
                                        <button
                                            onClick={() => setTimeRange(option.value)}
                                            className={classNames(
                                                'px-1 py-1 rounded-md transition-colors',
                                                timeRange === option.value
                                                    ? 'bg-white text-black shadow'
                                                    : 'hover:text-black'
                                            )}>
                                            {option.name}
                                        </button>
                                    </span>
                                ))}
                                <Popover className="relative">
                                    <PopoverButton
                                        className={classNames(
                                            !timeRange
                                                ? 'bg-white text-black shadow'
                                                : 'hover:text-black',
                                            'relative cursor-default rounded-md px-1 py-1 text-left text-gray-900  sm:text-sm sm:leading-6'
                                        )}>
                                        <CalendarDateRangeIcon className="h-4 w-4" />
                                    </PopoverButton>
                                    <PopoverPanel
                                        anchor={{
                                            to: 'bottom start'
                                        }}
                                        className="flex flex-col bg-white z-50 rounded-xl py-4 px-6 shadow-md gap-y-4">
                                        <DayPicker
                                            numberOfMonths={2}
                                            pagedNavigation
                                            defaultMonth={customRange.to}
                                            classNames={{
                                                months: 'flex gap-x-4',
                                                caption_label: 'leading-none',
                                                selected: '', // This somehow fixes the calendar style
                                                range_middle: 'bg-indigo-50',
                                                range_start:
                                                    'rounded-l-lg bg-indigo-600 text-gray-50',
                                                range_end:
                                                    'rounded-r-lg bg-indigo-600 text-gray-50',
                                                today: '!font-semibold'
                                            }}
                                            disabled={{ after: new Date() }}
                                            mode="range"
                                            selected={customRange}
                                            onSelect={handleSelect}
                                        />
                                    </PopoverPanel>
                                </Popover>
                            </div>
                        </div>
                    </div>
                }
                className="w-full bg-white p-4 border mt-4 rounded-lg">
                <div className="h-[32rem]">
                    <ParentSize>
                        {({ width, height }) => (
                            <ActivityChart
                                width={width}
                                height={height}
                                entity={entity}
                                activities={activities}
                                followers={followers}
                                timeSeries={[...(timeSeries?.values ?? [])].sort(
                                    (a, b) => (a.datetime < b.datetime ? -1 : 1)
                                )}
                                onClick={onClick}
                                filterBy={groupBy}
                                overlay={overlay}
                                timeRange={timeRange}
                            />
                        )}
                    </ParentSize>
                </div>
                <div className="flex justify-center gap-x-4 -mt-6">
                    {labels.map(({ label, color }) => {
                        return (
                            <div key={label} className="flex gap-x-2 items-center">
                                <div
                                    className="size-3 rounded-full"
                                    style={{ background: color }}
                                />
                                <span className="text-sm">{label}</span>
                            </div>
                        );
                    })}

                    <div className="flex items-stretch flex-row flex-nowrap">
                        <Dropdown
                            options={OVERLAY_OPTIONS}
                            selected={overlay}
                            onChange={setOverlay}
                            title="Overlay"
                            buttonClass={`border-solid ${!!overlay ? 'rounded-r-none' : ''}`}
                        />
                        {!!overlay && (
                            <Dropdown
                                options={[
                                    {
                                        name: (
                                            <span className="flex items-center gap-1 text-red-600">
                                                {' '}
                                                <TrashIcon className="h-4 w-4" />
                                                Clear Overlay
                                            </span>
                                        ),
                                        value: 'clear'
                                    }
                                ]}
                                selected={overlay}
                                onChange={() => setOverlay(undefined)}
                                buttonClass="rounded-l-none border-l-0"
                                placeholder={
                                    <span className="flex">
                                        <EllipsisVerticalIcon className="h-5 w-5" />
                                    </span>
                                }
                            />
                        )}
                    </div>
                </div>
            </ContentCard>
        </>
    );
}
