import { scaleLinear, scaleUtc } from '@visx/scale';
import React from 'react';
import { AxisBottom, AxisLeft, Orientation } from '@visx/axis';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { format, parse } from 'date-fns';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { extent } from '@visx/vendor/d3-array';
import { FollowerDataPoint } from './FollowersChart';
import { Activity } from '@quarterback/types';

const LEFT_PADDING = 48 + 18;
const RIGHT_PADDING = 18;
const BOTTOM_PADDING = 32;
const TOP_PADDING = 32;
const LABEL_INDICATOR_RADIUS = 6;

export default function Axes({
    xScale,
    height,
    quotes,
    yScalePadding,
    width,
    followers,
    overlay,
    timeRange
}: {
    xScale: AnyScaleBand;
    height: number;
    width: number;
    quotes: Array<TimeSeriesQuote>;
    yScalePadding: number;
    followers: FollowerDataPoint[];
    overlay?: string;
    timeRange?: string;
}) {
    const COLOUR = '#94a3b8';

    const minYValue = Math.min(...quotes.map((quote) => quote.low)) * yScalePadding;
    const maxYValue = Math.max(...quotes.map((quote) => quote.high));

    const yScale = scaleLinear<number>({
        range: [height - 32, 0],
        round: false,
        domain: [minYValue, maxYValue]
    });

    const yScaleTradeVolume = scaleLinear<number>({
        range: [height - 32, 0],
        round: true,
        domain: extent(quotes, (quote) => quote.volume) as number[]
    });

    const yScaleFollowers = scaleLinear<number>({
        range: [height - 32, 0],
        round: true,
        domain: extent(followers, (a) => a.total) as number[]
    });
    const yScaleSentiment = scaleLinear({
        domain: [-1, 1], // Sentiment range from -1 to 1
        range: [height - 32, 0]
    });

    const tickFormat = (value: string) => {
        if (typeof value !== 'string') return;
        if (value.length <= 3) return value;

        const date = parse(value, 'yyyy-MM-dd', new Date());

        // For 1W range, show weekday names
        if (timeRange === '1W') {
            return format(date, 'EEE'); // Mon, Tue, Wed, etc.
        }

        // For 1D range, show time intervals
        if (timeRange === '1D') {
            // If the value contains time information, format it as time
            if (value.includes('T') || value.includes(':')) {
                const timeDate = new Date(value);
                return format(timeDate, 'HH:mm'); // 09:00, 12:00, etc.
            }
            return format(date, 'HH:mm');
        }

        // Default format for other ranges
        return format(parse(value, 'yyyy-MM-dd', new Date()), 'MMM do');
    };
    const yScaleRight =
        overlay === 'followers'
            ? yScaleFollowers
            : overlay === 'sentiment'
                ? yScaleSentiment
                : yScaleTradeVolume;

    return (
        <>
            <AxisLeft
                top={TOP_PADDING}
                scale={yScale}
                orientation={Orientation.left}
                tickFormat={(a) => `$${a.valueOf().toFixed(4)}`}
                left={LEFT_PADDING}
                tickStroke={COLOUR}
                stroke={COLOUR}
                tickLabelProps={{ fill: COLOUR }}
            />
            <AxisLeft
                top={TOP_PADDING}
                scale={yScaleRight}
                orientation={Orientation.left}
                tickFormat={(a) => `${a}`}
                hideZero={overlay !== 'sentiment'}
                left={width - RIGHT_PADDING}
                tickStroke={COLOUR}
                stroke={COLOUR}
                tickLabelProps={{ fill: COLOUR }}
            />
            <AxisBottom
                left={LEFT_PADDING}
                orientation={Orientation.bottom}
                scale={xScale}
                tickFormat={tickFormat}
                top={height}
                tickStroke={COLOUR}
                stroke={COLOUR}
                rangePadding={{ end: 80 }}
                tickLabelProps={{
                    fill: COLOUR
                }}
            />

            <g transform={`translate(12, ${(height) / 2}) rotate(-90)`}>
                <circle r={LABEL_INDICATOR_RADIUS} fill="#16a34a" cx={-20} cy={0} />
                <text
                    x={-6}
                    y={4}
                    textAnchor="start"
                    fill="#16a34a"
                    fontSize={12}
                    fontWeight="bold"
                >
                    Share Price
                </text>
            </g>

            <g transform={`translate(${width - 8}, ${(height - TOP_PADDING) / 2}) rotate(90)`}>
                <circle r={LABEL_INDICATOR_RADIUS} fill="#7620f0" cx={-20} cy={0} />
                <text
                    x={-6}
                    y={4}
                    textAnchor="start"
                    fill="#7620f0"
                    fontSize={12}
                    fontWeight="bold"
                >
                    {overlay === 'followers'
                        ? 'Followers'
                        : overlay === 'sentiment'
                            ? 'Sentiment'
                            : 'Volume'}
                </text>
            </g>
        </>
    );
}
